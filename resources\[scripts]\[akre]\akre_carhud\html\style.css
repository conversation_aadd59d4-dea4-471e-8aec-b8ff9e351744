@import url('https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@400;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@700&display=swap');

body {
    margin: 0;
    padding: 0;
    overflow: hidden;
}

@keyframes slideInFromBottom {
    0% {
        transform: translateY(100%) scale(0.95);
        opacity: 0;
    }
    100% {
        transform: translateY(0) scale(1);
        opacity: 1;
    }
}

@keyframes slideFromRight {
    0% {
        transform: translateX(250%) scale(0.45);
        opacity: 0;
    }
    100% {
        transform: translateX(0) scale(1);
        opacity: 1;
    }
}

@keyframes slideFromRightFast {
    0% {
        transform: translateX(50%) scale(0.95);
        opacity: 0;
    }
    100% {
        transform: translateX(0) scale(1);
        opacity: 1;
    }
}

#hud {
    position: absolute;
    bottom: 20px;
    right: -80px;
    color: white;
    font-size: 100px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
}

#speed {
    font-weight: bold;
    letter-spacing: 2px;
    display: inline-block;
    min-width: 300px;
    text-align: left;
    font-family: 'Space Grotesk', sans-serif;
    font-variant-numeric: tabular-nums;
    animation: slideInFromBottom 0.5s ease-out forwards;
}

#gear-container {
    display: flex;
    align-items: center;
    margin-left: -100px;
    position: relative;
    animation: slideInFromBottom 0.5s ease-out forwards;
}

#gear {
    font-size: 20px;
    font-weight: bold;
    font-family: 'Roboto', sans-serif;
    margin-top: -10px;
    position: absolute;
    left: 30px;
}

#gear-icon {
    display: flex;
    width: 30px;
    height: 30px;
    margin-bottom: 25px;
    margin-right: 150px;
}

#fuel-container {
    display: flex;
    align-items: center;
    margin-left:5px;
    margin-top: 10px;
    animation: slideFromRight 0.5s ease-out forwards;
}

#fuel-icon {
    width: 25px;
    height: 25px;
    margin-left: -160px;
    margin-top: 42px;
}

#fuel-bar {
    width: 25px;
    height: 60px;
    background-color: rgba(116, 116, 116, 0.282);
    border-radius: 0;
    overflow: hidden;
    position: relative;
}

#fuel-level {
    width: 100%;
    background-color: white;
    position: absolute;
    bottom: 0;
    transition: height 0.5s ease;
}

.vehicle-damage {
    position: absolute;
    bottom: -10%;
    margin-left: 13%;
    transform: translateX(-50%);
    width: 200px;
    display: none;
    z-index: 9999;
}

.vehicle-damage img {
    animation: slideInFromBottom 0.5s ease-out forwards;
    width: 22%;
    height: auto;
    filter: drop-shadow(0 0 5px rgba(255, 255, 255, 0.8));
}

.engine-status {
    position: absolute;
    bottom: 20%;
    margin-left: 16.5%;
    transform: translateX(-50%);
    width: 200px;
    display: none;
    z-index: 9999;
}

.engine-status img {
    animation: slideInFromBottom 0.5s ease-out forwards;
    width: 13%;
    height: auto;
    filter: drop-shadow(0 0 5px rgba(255, 255, 255, 0.8));
}

.vehicle-seatbelt {
    display: none;
    position: fixed;
    top: 86.1vh;
    left: 95%;
    transform: translateX(-50%);
    padding: 10px;
    animation: blink 1s infinite;
}

@keyframes blink {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0;
    }
}

.vehicle-seatbelt img {
    animation: slideInFromBottom 0.5s ease-out forwards;
    width: 25px;
    height: 25px;
    filter: drop-shadow(0 0 5px rgba(255, 0, 0, 0.8));
}

.speed-limiter-icon {
    animation: slideInFromBottom 0.5s ease-out forwards;
    display: none;
    position: absolute;
    bottom: 14vh;
    right: 115%;
    height: 1%;
    width: 1%;
    filter: drop-shadow(0 0 5px rgba(150, 253, 86, 0.8));
}

.speed-limiter-icon img {
    width: 80px;
    height: auto;
}

.vehicle-abs {
    display: none;
    position: fixed;
    top: 81.2vh;
    left: 95.2%;
    transform: translateX(-50%);
    animation: fast-blink 0.2s infinite;
}

.vehicle-abs img {
    animation: slideFromRightFast 0.5s ease-out forwards;
    width: 30px;
    height: 30px;
    filter: drop-shadow(0 0 5px rgba(255, 255, 0, 0.8));
}

.vehicle-esp {
    display: none;
    position: fixed;
    top: 81.5vh;
    left: 97.8%;
    transform: translateX(-50%);
    animation: fast-blink 0.2s infinite;
}

.vehicle-esp img {
    animation: slideFromRightFast 0.5s ease-out forwards;
    width: 30px;
    height: 30px;
    filter: drop-shadow(0 0 5px rgba(255, 255, 0, 0.8));
}

@keyframes fast-blink {
    0%, 50% { opacity: 1; }
    50.1%, 100% { opacity: 0; }
}

@keyframes slideOutToBottom {
    0% {
        transform: translateY(0) scale(1);
        opacity: 1;
    }
    100% {
        transform: translateY(100%) scale(0.95);
        opacity: 0;
    }
}

#street-info {
    animation: slideInFromBottom 0.5s ease-out forwards;
    position: absolute;
    bottom: 20.5%;
    left: 1.5%;
    text-align: left;
    color: white;
    font-family: 'Space Grotesk', sans-serif;
}

#street-line {
    display: flex;
    align-items: center;
    font-size: 1rem;
    margin-bottom: -10px;
}

#area-line span {
    font-size: 0.8rem;
    margin-left: 50px;
    margin-top: 0;
    opacity: 0.8;
}

.icon-circle {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 37px;
    height: 37px;
    background:rgba(35, 35, 35, 0.69);
    box-shadow: 2px 5px 10px 0 rgba(0,0,0,0.20);
    border: 2px solid rgba(84, 83, 83, 0.365);
    border-radius: 50%;
    margin-right: 10px;
    transform: translateY(10px);
}

.icon-circle i {
    color: rgb(176, 176, 176);
    font-size: 20px;
}

#street-primary {
    font-weight: bold;
}

#area-line span {
    font-size: 1rem;
    margin-left: 50px;
    opacity: 0.8;
}

#heading-info {
    animation: slideInFromBottom 0.5s ease-out forwards;
    position: relative;
    top: 68vh;
    left: 4.11%;
    transform: translateX(-50%);
    text-align: left;
    color: white;
    font-family: 'Space Grotesk', sans-serif;
    font-size: 1.1rem;
    font-weight: bold;
    display: flex;
    align-items: center;
}

.icon-circle-heading {
    flex-shrink: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 37px;
    height: 37px;
    background:rgba(35, 35, 35, 0.69);
    box-shadow: 2px 5px 10px 0 rgba(0,0,0,0.20);
    border: 2px solid rgba(84, 83, 83, 0.365);
    border-radius: 50%;
    margin-left: -50px;
    transform: translateY(30px);
}

.icon-circle-heading i {
    color: rgb(176, 176, 176);
    font-size: 20px;
}

#heading-direction {
    white-space: nowrap;
}

#street-info {
    display: none;
}

#heading-info {
    display: none;
}


#heading-info:empty {
    display: none;
}

.icon-circle-heading {
    display: none;
}