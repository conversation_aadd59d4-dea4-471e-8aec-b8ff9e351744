Config = {}

Config.Unit = 'mph' -- mph/kmh
Config.CalculationFactor = 2.23694 -- if u want to custom calculate the speed select Config.Unit = 'mph' then change this 

Config.SeatBeltSound = 'beltalarm' -- Interact-sound
Config.SeatBeltVolume = 0.5

Config.BreakKey = 8 -- Key = S
Config.brakePressedTime = 760 -- ms

Config.Use = {
    heading = true,
    streets = true,

    abs = false, -- If the player holds the brake button for a longer period of time, the ABS light will illuminate to simulate full braking
    esp = false,  -- If the vehicle loses traction, the ESP warning light will flash

    seatbelt = true,
    speedlimiter = false,
}

Config.RefreshTime = { -- How often the functions will be updated in miliseconds 
    speed = 100,
    gearbox = 1000,
    fuel = 2500,
    vehicleengbodyhealt = 2500,
    streetsheading = 1000
}

Config.CardinalDirections = {
    [1] = {min = 22.5, max = 67.5, name = "Severovýchod"},
    [2] = {min = 67.5, max = 112.5, name = "V<PERSON><PERSON><PERSON>"},
    [3] = {min = 112.5, max = 157.5, name = "<PERSON>hov<PERSON>cho<PERSON>"},
    [4] = {min = 157.5, max = 202.5, name = "<PERSON>h"},
    [5] = {min = 202.5, max = 247.5, name = "Jihozápad"},
    [6] = {min = 247.5, max = 292.5, name = "Z<PERSON>pad"},
    [7] = {min = 292.5, max = 337.5, name = "Severozápad"},
    [8] = {min = 337.5, max = 360, name = "Sever"},
    [9] = {min = 0, max = 22.5, name = "Sever"}
}

Config.AllowedVehicleClasses = { -- Which vehicle category will have a working seat belt, abs and esp
    [0] = true,  -- Compacts
    [1] = true,  -- Sedans
    [2] = true,  -- SUVs
    [3] = true,  -- Coupes
    [4] = true,  -- Muscle
    [5] = true,  -- Sports Classics
    [6] = true,  -- Sports
    [7] = true,  -- Super
    [8] = true, -- Motorcycles
    [9] = true,  -- Off-road
    [10] = true, -- Industrial
    [11] = true, -- Utility
    [12] = true, -- Vans
    [13] = true, -- Cycles
    [14] = true, -- Boats
    [17] = true, -- Service
    [18] = true, -- Emergency
    [19] = true, -- Military
}