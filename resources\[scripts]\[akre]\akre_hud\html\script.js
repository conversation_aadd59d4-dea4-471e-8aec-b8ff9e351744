function areAllElementsHidden() {
    const elements = [
        document.getElementById("health-container"),
        document.getElementById("armor-container"),
        document.getElementById("hunger-container"),
        document.getElementById("thirst-container"),
        document.getElementById("stamina-container"),
        document.getElementById("oxygen-container")
    ];

    return elements.every(element => element.style.display === "none");
}

function updateVoicePosition() {
    const voiceContainer = document.getElementById("voice-container");

    if (areAllElementsHidden()) {
        voiceContainer.style.bottom = "35px";
    } else {
        voiceContainer.style.bottom = "85px";
    }
}

function showElement(element) {
    if (element.style.display === "none") {
        element.style.display = "flex";
        element.classList.remove("hide");
        element.classList.add("show");
    }
}

function hideElement(element) {
    if (element.style.display !== "none") {
        element.classList.remove("show");
        element.classList.add("hide");

        element.addEventListener("animationend", () => {
            element.style.display = "none";
        }, { once: true });
    }
}


window.addEventListener("message", (event) => {
    const data = event.data;

    if (data.action === "enableBlackBars") {
        const size = `${data.size}%`;
        document.getElementById("black-bar-top").style.height = size;
        document.getElementById("black-bar-bottom").style.height = size;
    } else if (data.action === "disableBlackBars") {
        document.getElementById("black-bar-top").style.height = "0%";
        document.getElementById("black-bar-bottom").style.height = "0%";
    }

    if (data.type === "hideHUD") {
        document.getElementById("hud-container").style.display = "none";
    }

    if (data.type === "showHUD") {
        document.getElementById("hud-container").style.display = "flex";
    }

    if (data.type === "shiftHUD") {
        const hudContainer = document.getElementById("hud-container");
    
        if (data.shiftRight) {
            hudContainer.classList.add("shift-right");
        } else {
            hudContainer.classList.remove("shift-right");
        }
    }
    
    if (data.type === "updateHealth") {
        const healthContainer = document.getElementById("health-container");

        if (data.show) {
            showElement(healthContainer);
            updateCircle("health-border", "health-icon", "health-text", data.health);
        } else {
            hideElement(healthContainer);
        }
        updateVoicePosition();
    }

    if (data.type === "updateArmor") {
        const armorContainer = document.getElementById("armor-container");

        if (data.show) {
            showElement(armorContainer);
            updateCircle("armor-border", "armor-icon", "armor-text", data.armor);
        } else {
            hideElement(armorContainer);
        }
        updateVoicePosition();
    }

    if (data.type === "updateStamina") {
        const staminaContainer = document.getElementById("stamina-container");

        if (data.alwaysShow || data.stamina < 100) {
            showElement(staminaContainer);
            updateCircle("stamina-border", "stamina-icon", "stamina-text", data.stamina);
        } else {
            hideElement(staminaContainer);
        }
        updateVoicePosition();
    }

    if (data.type === "updateNeeds") {
        const hungerContainer = document.getElementById("hunger-container");
        const thirstContainer = document.getElementById("thirst-container");

        if (data.showHunger) {
            showElement(hungerContainer);
            updateCircle("hunger-border", "hunger-icon", "hunger-text", data.hunger);
        } else {
            hideElement(hungerContainer);
        }

        if (data.showThirst) {
            showElement(thirstContainer);
            updateCircle("thirst-border", "thirst-icon", "thirst-text", data.thirst);
        } else {
            hideElement(thirstContainer);
        }

        updateVoicePosition();
    }

    if (data.type === "updateOxygen") {
        const oxygenContainer = document.getElementById("oxygen-container");

        showElement(oxygenContainer);
        updateCircle("oxygen-border", "oxygen-icon", "oxygen-text", data.oxygen);
        updateVoicePosition();
    }

    if (data.type === "hideOxygen") {
        const oxygenContainer = document.getElementById("oxygen-container");

        hideElement(oxygenContainer);
        updateVoicePosition();
    }

    if (data.type === "updateVoice") {
        const voiceIcon = document.getElementById("voice-icon");
        const dots = document.querySelectorAll(".dot");
    
        dots.forEach(dot => dot.classList.remove("active"));
    
        for (let i = 0; i < data.voiceLevel; i++) {
            dots[i]?.classList.add("active");
        }
    
        if (data.isTalking) {
            if (data.isRadioActive) {
                voiceIcon.classList.remove("fa-microphone");
                voiceIcon.classList.add("fa-tower-broadcast");
                voiceIcon.classList.add("talking-radio");
                voiceIcon.classList.remove("talking");
            } else {
                voiceIcon.classList.remove("fa-tower-broadcast");
                voiceIcon.classList.add("fa-microphone");
                voiceIcon.classList.add("talking");
                voiceIcon.classList.remove("talking-radio");
            }
        } else {
            voiceIcon.classList.remove("fa-tower-broadcast", "talking-radio");
            voiceIcon.classList.add("fa-microphone");
            voiceIcon.classList.remove("talking", "talking-radio");
        }
    }
});

function updateCircle(borderId, iconId, textId, value) {
    const borderElement = document.getElementById(borderId);
    const iconElement = document.getElementById(iconId);
    const textElement = document.getElementById(textId);

    const color = window.getComputedStyle(borderElement).borderColor;

    let numericValue = parseInt(value);
    if (isNaN(numericValue)) numericValue = 0;

    const percentage = Math.round(Math.max(0, Math.min(numericValue, 100)));

    borderElement.style.clipPath = `inset(${100 - percentage}% 0 0 0)`;
    iconElement.style.color = color;
    textElement.textContent = `${percentage}%`;
}