@import url('https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@400;700&display=swap');

.black-bar {
    position: fixed;
    left: 0;
    width: 100%;
    background-color: black;
    z-index: 9999;
}

.black-bar.top {
    top: 0;
}

.black-bar.bottom {
    bottom: 0;
}

body {
    margin: 0;
    padding: 0;
    background: transparent;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

#hud-container {
    position: fixed;
    bottom: 0;
    left: 0;
    padding: 20px;
    transition: transform 0.5s ease;
}

#hud-container.shift-right {
    transform: translateX(315px);
}

#hud {
    position: fixed;
    bottom: 35px;
    left: 31px;
    display: flex;
    gap: 10px;
    align-items: center;
}

.hud-item {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 14px;
    font-weight: bold;
    color: white;
    font-family: 'Space Grotesk', sans-serif;
    animation: slideIn 0.5s ease-out forwards;
}

.hud-item.hide {
    animation: slideOut 0.5s ease-in forwards;
}

.circle {
    position: relative;
    width: 40px;
    height: 40px;
    background:rgba(35, 35, 35, 0.69);
    box-shadow: 2px 5px 10px 0 rgba(0,0,0,0.20);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: slideIn 0.5s ease-out forwards;
}

.circle i {
    color: white;
    font-size: 20px;
    z-index: 1;
}

.circle.hide {
    animation: slideOut 0.5s ease-in forwards;
}

.border {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    border: 2.8px solid transparent;
    box-sizing: border-box;
    clip-path: inset(0 0 100% 0);
    transition: clip-path 0.3s ease-out;
    z-index: 0;
}

#health-border {
    border-color: #950933;
}

#armor-border {
    border-color: rgb(54, 103, 201);
}

#hunger-border {
    border-color: #f9cb6d;
}

#thirst-border {
    border-color: #70d9c3;
}

#stamina-border {
    border-color: #55ff7a;
}

#oxygen-border {
    border-color: #00aaff;
}

#stamina-container.hidden {
    display: none;
}

#voice-container {
    position: fixed;
    bottom: 85px;
    left: 31px;
    display: flex;
    align-items: center;
    background: rgba(35, 35, 35, 0.8);
    padding: 5px 10px;
    border-radius: 20px;
    gap: 5px;
    transition: bottom 0.3s ease;
}

#voice-container.lower {
    bottom: 35px;
}

#voice-icon {
    color: white;
    font-size: 16px;
}

#voice-level {
    display: flex;
    gap: 3px;
}

.dot {
    width: 6px;
    height: 6px;
    background: gray;
    border-radius: 50%;
    transition: background 0.2s ease-in-out;
}

.active {
    background: #007bff;
}

.talking {
    color: #f9f107 !important;
    animation: talkingAnimation 1s infinite ease-in-out;
}

.talking-radio {
    color: #ff66de !important;
    animation: talkingAnimation 1s infinite ease-in-out;
}

@keyframes talkingAnimation {
    0% { transform: translateY(0px); }
    50% { transform: translateY(-3px); }
    100% { transform: translateY(0px); }
}

@keyframes slideIn {
    from {
        transform: translateY(100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes slideOut {
    from {
        transform: translateY(0);
        opacity: 1;
        scale: 1;
    }
    to {
        transform: translateY(50%);
        opacity: 0;
        scale: 0.5;
    }
}