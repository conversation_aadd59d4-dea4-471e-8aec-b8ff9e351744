local QBCore = exports['qb-core']:GetCoreObject()

local seatbeltOn = false
local beepActive = false

RegisterNetEvent('seatbelt:client:ToggleSeatbelt', function(state)
    seatbeltOn = state
    if seatbeltOn then
        SendNUIMessage({
            action = "hideSeatbeltWarning"
        })
        beepActive = false
    else
        SendNUIMessage({
            action = "showSeatbeltWarning"
        })
        beepActive = true
        TriggerEvent('seatbelt:client:PlayBeep')
    end
end)

CreateThread(function()
    while true do
        if not Config.Use.seatbelt then return end
        local ped = PlayerPedId()
        local vehicle = GetVehiclePedIsIn(ped, false)

        if vehicle ~= 0 and GetPedInVehicleSeat(vehicle, -1) == ped then
            local vehicleClass = GetVehicleClass(vehicle)

            if Config.AllowedVehicleClasses[vehicleClass] then
                if not seatbeltOn then
                    beepActive = true
                    TriggerServerEvent("InteractSound_SV:PlayOnSource", Config.SeatBeltSound, Config.SeatBeltVolume)
                    SendNUIMessage({ action = "showSeatbeltWarning" })
                end
            else
                if beepActive then
                    beepActive = false
                    SendNUIMessage({ action = "hideSeatbeltWarning" })
                end
            end
        else
            if beepActive then
                beepActive = false
                SendNUIMessage({ action = "hideSeatbeltWarning" })
            end
        end
        Wait(2000)
    end
end)

RegisterNetEvent("hud:client:UpdateSpeedLimiter", function(state)
    if not Config.Use.speedlimiter then return end
    SendNUIMessage({
        action = state and "showSpeedLimiterIcon" or "hideSpeedLimiterIcon"
    })
end)