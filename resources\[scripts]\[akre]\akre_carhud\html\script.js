window.addEventListener('message', (event) => {
    const data = event.data;

    if (data.type === "showHUD") {
        document.getElementById('hud').style.display = 'flex';
        document.getElementById('street-info').style.display = 'block';
        document.getElementById('heading-info').style.display = 'block';
        document.querySelector('.icon-circle-heading').style.display = 'flex';
    } 
    
    if (data.type === "hideHUD") {
        document.getElementById('hud').style.display = 'none';
        document.getElementById('street-info').style.display = 'none';
        document.getElementById('heading-info').style.display = 'none';
        document.querySelector('.icon-circle-heading').style.display = 'none';
    }

    if (data.type === "updateVehicleData") {
        if (data.speed !== undefined) {
            document.getElementById('speed').innerText = data.speed;
        }
        if (data.gear !== undefined) {
            document.getElementById('gear').innerText = data.gear;
        }
        if (data.fuel !== undefined) {
            let fuelLevel = document.getElementById('fuel-level');
            fuelLevel.style.height = `${data.fuel}%`;
    
            if (data.fuel <= 15) {
                fuelLevel.style.backgroundColor = "red";
            } else if (data.fuel <= 25) {
                fuelLevel.style.backgroundColor = "yellow";
            } else {
                fuelLevel.style.backgroundColor = "white";
            }
        }
    }

    if (data.action === "updateBodyHealth") {
        const vehicleDamage = document.querySelector('.vehicle-damage');
        const vehicleDamageImage = vehicleDamage.querySelector('img');

        if (data.health < 35) {
            vehicleDamage.style.display = "block";
            vehicleDamageImage.src = "images/vehicle-body-critical.png";
            vehicleDamageImage.style.filter = "drop-shadow(0 0 10px rgba(255, 0, 0, 0.8))";
        } else if (data.health < 75) {
            vehicleDamage.style.display = "block";
            vehicleDamageImage.src = "images/vehicle-body.png";
            vehicleDamageImage.style.filter = "drop-shadow(0 0 10px rgba(255, 255, 0, 0.8))";
        } else {
            vehicleDamage.style.display = "none";
        }
    }

    if (data.action === "updateEngineHealth") {
        const engineStatus = document.querySelector('.engine-status');
        const engineImage = engineStatus.querySelector('img');

        if (data.health < 35) {
            engineStatus.style.display = "block";
            engineImage.src = "images/vehicle-engine-critical.png";
            engineImage.style.filter = "drop-shadow(0 0 10px rgba(255, 0, 0, 0.8))";
        } else if (data.health < 75) {
            engineStatus.style.display = "block";
            engineImage.src = "images/vehicle-engine.png";
            engineImage.style.filter = "drop-shadow(0 0 10px rgba(255, 255, 0, 0.8))";
        } else {
            engineStatus.style.display = "none";
        }
    }

    if (data.action === "showSeatbeltWarning") {
        document.querySelector('.vehicle-seatbelt').style.display = 'block';
    } else if (data.action === "hideSeatbeltWarning") {
        document.querySelector('.vehicle-seatbelt').style.display = 'none';
    }

    if (data.action === "showSpeedLimiterIcon") {
        document.querySelector('.speed-limiter-icon').style.display = 'block';
    } else if (data.action === "hideSpeedLimiterIcon") {
        document.querySelector('.speed-limiter-icon').style.display = 'none';
    }

    if (data.action === "showABS") {
        document.querySelector('.vehicle-abs').style.display = 'block';
    } else if (data.action === "hideABS") {
        document.querySelector('.vehicle-abs').style.display = 'none';
    }

    if (data.action === "showESP") {
        document.querySelector('.vehicle-esp').style.display = 'block';
    } else if (data.action === "hideESP") {
        document.querySelector('.vehicle-esp').style.display = 'none';
    }

    if (data.action === "updateStreetAndArea") {
        const streetPrimary = document.getElementById('street-primary');
        const areaName = document.getElementById('area-name');
        const streetInfo = document.getElementById('street-info');
        const cardinalDirection = document.getElementById('heading-info');
    
        streetPrimary.textContent = `${data.streetPrimary}${data.streetSecondary ? ' & ' + data.streetSecondary : ''}`;
        areaName.textContent = data.areaName || "";
    
        if (!data.streetPrimary && !data.streetSecondary && (!data.areaName || data.areaName === "Unknown Area")) {
            streetInfo.style.display = "none";
        } else {
            streetInfo.style.display = "block";
        }
    }
    
    if (data.action === "updateHeading") {
        const headingInfo = document.getElementById('heading-info');
        const headingDirection = document.getElementById('heading-direction');
        const iconCircle = document.querySelector('.icon-circle-heading');
    
        headingDirection.textContent = data.direction || "";
    
        if (!data.direction) {
            headingInfo.style.display = "none";
            iconCircle.style.display = "none";
        } else {
            headingInfo.style.display = "block";
            iconCircle.style.display = "flex";
        }
    }
});